"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { services } from "@/app/data/navItems";
import { Section } from "@/components/Section/Section";
import { ArrowUpRight, ChevronDown, ChevronUp, ChevronLeft } from "lucide-react";
import { useTranslations, useLocale } from "next-intl";

const Page = () => {
  const [hoveredService, setHoveredService] = useState<string | null>(null);
  const [activeSection, setActiveSection] = useState<string>("");
  const [expandedServices, setExpandedServices] = useState<Record<string, boolean>>(() => {
    // Initialize all services as collapsed by default
    const initial: Record<string, boolean> = {};
    services.items.forEach((service) => {
      initial[service.title] = false;
    });
    return initial;
  });
  const t = useTranslations("ServicesSection");
  const tItems = useTranslations("ServiceItems");
  const locale = useLocale();

  // Handle scroll to section
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  };

  // Toggle service expansion
  const toggleServiceExpansion = (serviceTitle: string) => {
    setExpandedServices(prev => ({
      ...prev,
      [serviceTitle]: !prev[serviceTitle]
    }));
  };

  // Track active section on scroll
  useEffect(() => {
    const handleScroll = () => {
      const sections = services.items.map((service) => service.title);
      const scrollPosition = window.scrollY + 100;

      for (const sectionId of sections) {
        const element = document.getElementById(sectionId);
        if (element) {
          const { offsetTop, offsetHeight } = element;
          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
            setActiveSection(sectionId);
            break;
          }
        }
      }
    };

    window.addEventListener("scroll", handleScroll);
    handleScroll(); // Set initial active section
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);
  return (
    <div className="mt-23">
      <div className="py-4 md:py-10 relative overflow-hidden">
        <div className="absolute inset-0 bg-[#4730A4]" />
        <div
          className={`bg-[url(/contactFlowMobile.svg)] md:bg-[url(/contactFlow.svg)] w-full absolute inset-0 left-0 bg-no-repeat bg-center bg-cover mix-blend-luminosity ${
            locale === "en" ? "rotate-0" : "rotate-180"
          }`}
        />
        <div className="relative z-10 container mx-auto">
          <div className="w-full px-4  text-start text-white mx-auto ">
            <h2 className="text-2xl sm:text-xl md:text-[32px] font-bold mb-4 md:mb-6 mt-5">
              {tItems("heading")}
            </h2>
            <p className="text-[12px] md:text-base mb-6 md:mb-8 text-[#E2DAFF] leading-relaxed">
              {tItems("description")}
            </p>
          </div>
        </div>
      </div>
      <Section container variant="white" padding="default" relative >
        <div className=" mx-auto px-4 relative  z-10">
          <div className="flex gap-8 justify-between">
            {/* Sidebar Table of Contents */}
            <div className="hidden lg:block w-110 sticky top-24 h-fit">
              <div className="bg-white p-6 ">
                <nav className="space-y-2">
                  {services.items.map((service, index) => (
                    <div key={index} className="space-y-1">
                      {/* Main Service */}
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => scrollToSection(service.title)}
                          className={`flex-1 text-start px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                            activeSection === service.title
                              ? "bg-[#5840BA] text-white"
                              : "text-gray-700 hover:bg-gray-100"
                          }`}
                        >
                          {t(service.title)}
                        </button>
                        <button
                          onClick={() => toggleServiceExpansion(service.title)}
                          className="p-1 hover:bg-gray-100 rounded transition-colors"
                        >
                          {expandedServices[service.title] ? (
                            <ChevronUp className="w-4 h-4 text-gray-600" />
                          ) : (
                          <ChevronDown className="w-4 h-4 text-gray-600" />
                          )}
                        </button>
                      </div>

                      {/* Sub-services */}
                      {service.items &&
                        service.items.length > 0 &&
                        expandedServices[service.title] && (
                          <div className="mr-8 space-y-1 transition-all duration-200">
                            {service.items.map((subService, subIndex) => (
                              <button
                                key={subIndex}
                                onClick={() =>
                                  scrollToSection(
                                    `${service.title}-${subService.title}`
                                  )
                                }
                                className="w-full text-right px-3 py-1 rounded-md text-xs text-gray-600 hover:bg-gray-50 hover:text-gray-900 transition-colors flex items-center justify-start gap-2"
                              >
                                <span>{tItems(subService.title)}</span>
                                <ChevronLeft className={`w-3 h-3 ${locale === "en" ? "rotate-180" : "rotate-0"}`} />
                              </button>
                            ))}
                          </div>
                        )}
                    </div>
                  ))}
                </nav>
              </div>
            </div>

            {/* Main Content */}
            <div className="flex-1">
              {/* Services Sections */}
              <div className="space-y-16">
                {services.items.map((service, serviceIndex) => (
                  <div
                    key={serviceIndex}
                    id={service.title}
                    className="space-y-8 scroll-mt-24"
                  >
                    {/* Service Title and Description */}
                    <div className="text-start space-y-4">
                      <h2 className="text-3xl font-bold text-gray-900">
                        {t(service.title)}
                      </h2>
                      <p className="text-base text-[#525252] ">
                        {t(service.description)}
                      </p>
                    </div>

                    {/* Sub-services Cards */}
                    {service.items && service.items.length > 0 && (
                      <div className="grid md:grid-cols-2 lg:grid-cols-3  justify-center gap-4">
                        {service.items.map((subService, subIndex) => (
                          <Card
                            key={subIndex}
                            id={`${service.title}-${subService.title}`}
                            className={`  group relative w-full max-w-[322px] mx-auto h-[192px] transition-all duration-300 cursor-pointer shadow-none border-2 scroll-mt-24 ${
                              hoveredService === subService.title
                                ? "border-[#5840BA] shadow-[0px_0px_9px_0px_#5840BABF] bg-white"
                                : "border-[#ECECEC] hover:border-[#5840BA] bg-white"
                            }`}
                            onMouseEnter={() =>
                              setHoveredService(subService.title)
                            }
                            onMouseLeave={() => setHoveredService(null)}
                          >
                            <CardContent
                              className={`h-full flex flex-col transition-all duration-300 ${
                                hoveredService === subService.title
                                  ? "pt-0"
                                  : "pt-0"
                              }`}
                            >
                              {/* Icon */}
                              <div className="mb-[8px]">
                                <div
                                  className={`transition-colors w-[36px] h-[36px] duration-300 ${
                                    hoveredService === subService.title
                                      ? "text-[#5840BA] [&_svg]:!fill-[#5840BA] [&_svg]:!stroke-[#5840BA] [&_svg]:!color-[#5840BA] [&_svg_path]:!fill-[#5840BA] [&_svg_path]:!stroke-[#5840BA] [&_svg_circle]:!fill-[#5840BA] [&_svg_circle]:!stroke-[#5840BA]"
                                      : "text-[#5840BA] md:text-[#6D6D6D] [&_svg]:!fill-[#636363] [&_svg]:!stroke-[#636363] [&_svg]:!color-[#636363] [&_svg_path]:!fill-[#636363] [&_svg_path]:!stroke-[#636363] [&_svg_circle]:!fill-[#636363] [&_svg_circle]:!stroke-[#636363]"
                                  }`}
                                >
                                  {subService.icon}
                                </div>
                              </div>

                              {/* Content */}
                              <div className="flex-1">
                                <h3 className="text-lg font-semibold text-[#424242] mb-[12px] leading-tight hover:text-[#000]">
                                  {tItems(subService.title)}
                                </h3>
                                <p className="text-gray-600 text-sm leading-relaxed">
                                  {tItems(subService.description)}
                                </p>
                              </div>

                              <div
                                className={`transition-opacity duration-300 ${
                                  hoveredService === subService.title
                                    ? "opacity-100 inline-flex items-center text-[#5840BA] hover:text-[#5840BA] font-medium group/link text-sm"
                                    : "lg:opacity-0 lg:invisible opacity-100 text-[#5840BA] text-sm flex mt-2"
                                }`}
                              >
                                <Link
                                  href={subService.href}
                                  className="flex underline items-center"
                                >
                                  <span>
                                    {t("moreAbout")}{" "}
                                    {tItems(subService.title)
                                      .split(" ")
                                      .slice(0, 2)
                                      .join(" ")}
                                  </span>
                                  <ArrowUpRight className="w-4 h-4 mr-2" />
                                </Link>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </Section>
    </div>
  );
};

export default Page;
